"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Dynamically import SyntaxHighlighter to avoid SSR issues and Node.js package conflicts\nconst SyntaxHighlighter = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-syntax-highlighter\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900 text-gray-100 p-4 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: \"Loading syntax highlighter...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, undefined)\n});\nconst oneDark = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-syntax-highlighter/dist/esm/styles/prism\"\n        ]\n    },\n    ssr: false\n});\nfunction MarkdownRenderer({ content, className = '' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n            ],\n            components: {\n                // Headers\n                h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, void 0),\n                h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, void 0),\n                h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, void 0),\n                h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, void 0),\n                // Paragraphs\n                p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, void 0),\n                // Bold and italic\n                strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-bold text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, void 0),\n                em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, void 0),\n                // Lists\n                ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, void 0),\n                ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, void 0),\n                li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"leading-relaxed text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, void 0),\n                // Code blocks and inline code\n                code: ({ node, inline, className, children, ...props })=>{\n                    const match = /language-(\\w+)/.exec(className || '');\n                    const language = match ? match[1] : '';\n                    const codeContent = String(children).replace(/\\n$/, '');\n                    if (!inline) {\n                        // Handle code blocks (both with and without language detection)\n                        if (language && SyntaxHighlighter) {\n                            // Code block with syntax highlighting\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            text: codeContent,\n                                            variant: \"code\",\n                                            size: \"sm\",\n                                            title: \"Copy code\",\n                                            className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SyntaxHighlighter, {\n                                        style: oneDark || {},\n                                        language: language,\n                                        PreTag: \"div\",\n                                        className: \"text-sm\",\n                                        ...props,\n                                        children: codeContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 19\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 17\n                            }, void 0);\n                        } else {\n                            // Code block without language (plain text code block)\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            text: codeContent,\n                                            variant: \"code\",\n                                            size: \"sm\",\n                                            title: \"Copy code\",\n                                            className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: codeContent\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 19\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, void 0);\n                        }\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Blockquotes\n                blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, void 0),\n                // Links\n                a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, void 0),\n                // Tables\n                table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto my-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-gray-200 rounded-lg\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, void 0),\n                thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, void 0),\n                tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"divide-y divide-gray-200 bg-white\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, void 0),\n                tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: \"hover:bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, void 0),\n                th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, void 0),\n                td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, void 0),\n                // Horizontal rule\n                hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"my-4 border-gray-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, void 0)\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;