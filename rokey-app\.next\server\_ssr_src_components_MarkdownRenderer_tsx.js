"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Simple fallback for syntax highlighting to avoid webpack issues\nconst SimpleSyntaxHighlighter = ({ children, language })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\n// Simple markdown parser to avoid the debug package issue\nconst parseMarkdown = (content)=>{\n    // Basic markdown parsing without external dependencies\n    let html = content// Headers\n    .replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^# (.*$)/gim, '<h1>$1</h1>')// Bold\n    .replace(/\\*\\*(.*)\\*\\*/gim, '<strong>$1</strong>')// Italic\n    .replace(/\\*(.*)\\*/gim, '<em>$1</em>')// Code blocks\n    .replace(/```(\\w+)?\\n([\\s\\S]*?)```/gim, (match, lang, code)=>{\n        return `<pre data-language=\"${lang || ''}\" class=\"code-block\">${code.trim()}</pre>`;\n    })// Inline code\n    .replace(/`([^`]+)`/gim, '<code class=\"inline-code\">$1</code>')// Links\n    .replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/gim, '<a href=\"$2\" target=\"_blank\" rel=\"noopener noreferrer\">$1</a>')// Line breaks\n    .replace(/\\n/gim, '<br>');\n    return html;\n};\nfunction MarkdownRenderer({ content, className = '' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n            remarkPlugins: [\n                remarkGfm\n            ],\n            components: {\n                // Headers\n                h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, void 0),\n                h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, void 0),\n                h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, void 0),\n                h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, void 0),\n                // Paragraphs\n                p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, void 0),\n                // Bold and italic\n                strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-bold text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, void 0),\n                em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, void 0),\n                // Lists\n                ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, void 0),\n                ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, void 0),\n                li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"leading-relaxed text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, void 0),\n                // Code blocks and inline code\n                code: ({ node, inline, className, children, ...props })=>{\n                    const match = /language-(\\w+)/.exec(className || '');\n                    const language = match ? match[1] : '';\n                    const codeContent = String(children).replace(/\\n$/, '');\n                    if (!inline) {\n                        // Handle code blocks (both with and without language detection)\n                        if (language) {\n                            // Code block with simple syntax highlighting\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            text: codeContent,\n                                            variant: \"code\",\n                                            size: \"sm\",\n                                            title: \"Copy code\",\n                                            className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSyntaxHighlighter, {\n                                        language: language,\n                                        children: codeContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 19\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 17\n                            }, void 0);\n                        } else {\n                            // Code block without language (plain text code block)\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            text: codeContent,\n                                            variant: \"code\",\n                                            size: \"sm\",\n                                            title: \"Copy code\",\n                                            className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: codeContent\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 19\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 17\n                            }, void 0);\n                        }\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Blockquotes\n                blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, void 0),\n                // Links\n                a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, void 0),\n                // Tables\n                table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto my-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-gray-200 rounded-lg\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, void 0),\n                thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, void 0),\n                tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"divide-y divide-gray-200 bg-white\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, void 0),\n                tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: \"hover:bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, void 0),\n                th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, void 0),\n                td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, void 0),\n                // Horizontal rule\n                hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"my-4 border-gray-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, void 0)\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;