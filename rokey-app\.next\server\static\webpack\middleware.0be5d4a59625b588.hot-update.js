"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    // Skip middleware for static files and Next.js internal routes\n    const pathname = req.nextUrl.pathname;\n    if (pathname.startsWith('/_next/static') || pathname.startsWith('/_next/image') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.includes('.')) {\n        return res;\n    }\n    // Temporary bypass for development if Supabase connectivity issues\n    if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n        console.log('Middleware: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n        return res;\n    }\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname (already defined above)\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>pathname === route || pathname.startsWith(route));\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the user with error handling for network issues (more secure than getSession)\n    let session = null;\n    try {\n        const { data: { user } } = await supabase.auth.getUser();\n        // Create a session-like object for compatibility\n        session = user ? {\n            user\n        } : null;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier').eq('id', session.user.id).single();\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // If we can't fetch profile due to network issues, allow access\n                return res;\n            }\n            // If no profile or inactive subscription, redirect to pricing\n            if (!profile || profile.subscription_status !== 'active') {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL21pZGRsZXdhcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFtRDtBQUNSO0FBR3BDLGVBQWVFLFdBQVdDLEdBQWdCO0lBQy9DLElBQUlDLE1BQU1ILHFEQUFZQSxDQUFDSSxJQUFJLENBQUM7UUFDMUJDLFNBQVM7WUFDUEMsU0FBU0osSUFBSUksT0FBTztRQUN0QjtJQUNGO0lBRUEsK0RBQStEO0lBQy9ELE1BQU1DLFdBQVdMLElBQUlNLE9BQU8sQ0FBQ0QsUUFBUTtJQUNyQyxJQUNFQSxTQUFTRSxVQUFVLENBQUMsb0JBQ3BCRixTQUFTRSxVQUFVLENBQUMsbUJBQ3BCRixTQUFTRSxVQUFVLENBQUMsbUJBQ3BCRixTQUFTRSxVQUFVLENBQUMsZUFDcEJGLFNBQVNHLFFBQVEsQ0FBQyxNQUNsQjtRQUNBLE9BQU9QO0lBQ1Q7SUFFQSxtRUFBbUU7SUFDbkUsSUFBSVEsS0FBc0MsSUFBSUEsUUFBUUMsR0FBRyxDQUFDQyxzQkFBc0IsS0FBSyxRQUFRO1FBQzNGQyxRQUFRQyxHQUFHLENBQUM7UUFDWixPQUFPWjtJQUNUO0lBRUEsTUFBTWEsV0FBV2pCLGlFQUFrQkEsQ0FDakNZLDBDQUFvQyxFQUNwQ0Esa05BQXlDLEVBQ3pDO1FBQ0VRLFNBQVM7WUFDUEMsS0FBSUMsSUFBWTtnQkFDZCxPQUFPbkIsSUFBSWlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxPQUFPQztZQUNoQztZQUNBQyxLQUFJRixJQUFZLEVBQUVDLEtBQWEsRUFBRUUsT0FBWTtnQkFDM0N0QixJQUFJaUIsT0FBTyxDQUFDSSxHQUFHLENBQUM7b0JBQ2RGO29CQUNBQztvQkFDQSxHQUFHRSxPQUFPO2dCQUNaO2dCQUNBckIsTUFBTUgscURBQVlBLENBQUNJLElBQUksQ0FBQztvQkFDdEJDLFNBQVM7d0JBQ1BDLFNBQVNKLElBQUlJLE9BQU87b0JBQ3RCO2dCQUNGO2dCQUNBSCxJQUFJZ0IsT0FBTyxDQUFDSSxHQUFHLENBQUM7b0JBQ2RGO29CQUNBQztvQkFDQSxHQUFHRSxPQUFPO2dCQUNaO1lBQ0Y7WUFDQUMsUUFBT0osSUFBWSxFQUFFRyxPQUFZO2dCQUMvQnRCLElBQUlpQixPQUFPLENBQUNJLEdBQUcsQ0FBQztvQkFDZEY7b0JBQ0FDLE9BQU87b0JBQ1AsR0FBR0UsT0FBTztnQkFDWjtnQkFDQXJCLE1BQU1ILHFEQUFZQSxDQUFDSSxJQUFJLENBQUM7b0JBQ3RCQyxTQUFTO3dCQUNQQyxTQUFTSixJQUFJSSxPQUFPO29CQUN0QjtnQkFDRjtnQkFDQUgsSUFBSWdCLE9BQU8sQ0FBQ0ksR0FBRyxDQUFDO29CQUNkRjtvQkFDQUMsT0FBTztvQkFDUCxHQUFHRSxPQUFPO2dCQUNaO1lBQ0Y7UUFDRjtJQUNGO0lBR0YsMkNBQTJDO0lBRTNDLGtEQUFrRDtJQUNsRCxNQUFNRSxlQUFlO1FBQ25CO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELCtDQUErQztJQUMvQyxNQUFNQyxrQkFBa0I7UUFDdEI7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELCtCQUErQjtJQUMvQixNQUFNQyxnQkFBZ0JGLGFBQWFHLElBQUksQ0FBQ0MsQ0FBQUEsUUFBU3ZCLGFBQWF1QixTQUFTdkIsU0FBU0UsVUFBVSxDQUFDcUI7SUFDM0YsTUFBTUMsbUJBQW1CSixnQkFBZ0JFLElBQUksQ0FBQ0MsQ0FBQUEsUUFBU3ZCLFNBQVNFLFVBQVUsQ0FBQ3FCO0lBRTNFLHFDQUFxQztJQUNyQyxJQUFJRixpQkFBaUJHLGtCQUFrQjtRQUNyQyxPQUFPNUI7SUFDVDtJQUVBLG9GQUFvRjtJQUNwRixJQUFJNkIsVUFBVTtJQUNkLElBQUk7UUFDRixNQUFNLEVBQ0pDLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQ2YsR0FBRyxNQUFNbEIsU0FBU21CLElBQUksQ0FBQ0MsT0FBTztRQUMvQixpREFBaUQ7UUFDakRKLFVBQVVFLE9BQU87WUFBRUE7UUFBSyxJQUFJO0lBQzlCLEVBQUUsT0FBT0csT0FBTztRQUNkdkIsUUFBUXVCLEtBQUssQ0FBQyxvREFBb0RBO1FBQ2xFLGdFQUFnRTtRQUNoRSxvRUFBb0U7UUFDcEUsT0FBT2xDO0lBQ1Q7SUFFQSx5RUFBeUU7SUFDekUsSUFBSSxDQUFDNkIsU0FBUztRQUNaLE1BQU1NLGNBQWMsSUFBSUMsSUFBSSxnQkFBZ0JyQyxJQUFJc0MsR0FBRztRQUNuREYsWUFBWUcsWUFBWSxDQUFDbEIsR0FBRyxDQUFDLGNBQWNoQjtRQUMzQyxPQUFPUCxxREFBWUEsQ0FBQzBDLFFBQVEsQ0FBQ0o7SUFDL0I7SUFFQSx1RUFBdUU7SUFDdkUsSUFBSS9CLFNBQVNFLFVBQVUsQ0FBQyxpQkFBaUJGLFNBQVNFLFVBQVUsQ0FBQyxrQkFBa0JGLFNBQVNFLFVBQVUsQ0FBQyxZQUFZRixTQUFTRSxVQUFVLENBQUMsaUJBQWlCRixTQUFTRSxVQUFVLENBQUMsZ0JBQWdCRixTQUFTRSxVQUFVLENBQUMsb0JBQW9CO1FBQzlOLHFEQUFxRDtRQUNyRCxJQUFJO1lBQ0YsTUFBTSxFQUFFd0IsTUFBTVUsT0FBTyxFQUFFTixPQUFPTyxZQUFZLEVBQUUsR0FBRyxNQUFNNUIsU0FDbEQ2QixJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQywwQ0FDUEMsRUFBRSxDQUFDLE1BQU1mLFFBQVFFLElBQUksQ0FBQ2MsRUFBRSxFQUN4QkMsTUFBTTtZQUVULElBQUlMLGNBQWM7Z0JBQ2hCOUIsUUFBUXVCLEtBQUssQ0FBQyw0Q0FBNENPO2dCQUMxRCxnRUFBZ0U7Z0JBQ2hFLE9BQU96QztZQUNUO1lBRUEsOERBQThEO1lBQzlELElBQUksQ0FBQ3dDLFdBQVdBLFFBQVFPLG1CQUFtQixLQUFLLFVBQVU7Z0JBQ3hELE1BQU1aLGNBQWMsSUFBSUMsSUFBSSxZQUFZckMsSUFBSXNDLEdBQUc7Z0JBQy9DRixZQUFZRyxZQUFZLENBQUNsQixHQUFHLENBQUMsWUFBWTtnQkFDekNlLFlBQVlHLFlBQVksQ0FBQ2xCLEdBQUcsQ0FBQyxXQUFXO2dCQUN4QyxPQUFPdkIscURBQVlBLENBQUMwQyxRQUFRLENBQUNKO1lBQy9CO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2R2QixRQUFRdUIsS0FBSyxDQUFDLHFEQUFxREE7WUFDbkUsMkNBQTJDO1lBQzNDLE1BQU1DLGNBQWMsSUFBSUMsSUFBSSxZQUFZckMsSUFBSXNDLEdBQUc7WUFDL0NGLFlBQVlHLFlBQVksQ0FBQ2xCLEdBQUcsQ0FBQyxZQUFZO1lBQ3pDZSxZQUFZRyxZQUFZLENBQUNsQixHQUFHLENBQUMsV0FBVztZQUN4QyxPQUFPdkIscURBQVlBLENBQUMwQyxRQUFRLENBQUNKO1FBQy9CO0lBQ0Y7SUFFQSxPQUFPbkM7QUFDVDtBQUVPLE1BQU1nRCxTQUFTO0lBQ3BCQyxTQUFTO1FBQ1A7Ozs7OztLQU1DLEdBQ0Q7S0FDRDtBQUNILEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxtaWRkbGV3YXJlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNlcnZlckNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zc3InO1xuaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHR5cGUgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG1pZGRsZXdhcmUocmVxOiBOZXh0UmVxdWVzdCkge1xuICBsZXQgcmVzID0gTmV4dFJlc3BvbnNlLm5leHQoe1xuICAgIHJlcXVlc3Q6IHtcbiAgICAgIGhlYWRlcnM6IHJlcS5oZWFkZXJzLFxuICAgIH0sXG4gIH0pO1xuXG4gIC8vIFNraXAgbWlkZGxld2FyZSBmb3Igc3RhdGljIGZpbGVzIGFuZCBOZXh0LmpzIGludGVybmFsIHJvdXRlc1xuICBjb25zdCBwYXRobmFtZSA9IHJlcS5uZXh0VXJsLnBhdGhuYW1lO1xuICBpZiAoXG4gICAgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL19uZXh0L3N0YXRpYycpIHx8XG4gICAgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL19uZXh0L2ltYWdlJykgfHxcbiAgICBwYXRobmFtZS5zdGFydHNXaXRoKCcvZmF2aWNvbi5pY28nKSB8fFxuICAgIHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9wdWJsaWMvJykgfHxcbiAgICBwYXRobmFtZS5pbmNsdWRlcygnLicpXG4gICkge1xuICAgIHJldHVybiByZXM7XG4gIH1cblxuICAvLyBUZW1wb3JhcnkgYnlwYXNzIGZvciBkZXZlbG9wbWVudCBpZiBTdXBhYmFzZSBjb25uZWN0aXZpdHkgaXNzdWVzXG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiBwcm9jZXNzLmVudi5CWVBBU1NfQVVUSF9NSURETEVXQVJFID09PSAndHJ1ZScpIHtcbiAgICBjb25zb2xlLmxvZygnTWlkZGxld2FyZTogQnlwYXNzaW5nIGF1dGggY2hlY2tzIGR1ZSB0byBCWVBBU1NfQVVUSF9NSURETEVXQVJFPXRydWUnKTtcbiAgICByZXR1cm4gcmVzO1xuICB9XG5cbiAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2ZXJDbGllbnQoXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXG4gICAge1xuICAgICAgY29va2llczoge1xuICAgICAgICBnZXQobmFtZTogc3RyaW5nKSB7XG4gICAgICAgICAgcmV0dXJuIHJlcS5jb29raWVzLmdldChuYW1lKT8udmFsdWU7XG4gICAgICAgIH0sXG4gICAgICAgIHNldChuYW1lOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcsIG9wdGlvbnM6IGFueSkge1xuICAgICAgICAgIHJlcS5jb29raWVzLnNldCh7XG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgdmFsdWUsXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJlcyA9IE5leHRSZXNwb25zZS5uZXh0KHtcbiAgICAgICAgICAgIHJlcXVlc3Q6IHtcbiAgICAgICAgICAgICAgaGVhZGVyczogcmVxLmhlYWRlcnMsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJlcy5jb29raWVzLnNldCh7XG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgdmFsdWUsXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgICAgICByZW1vdmUobmFtZTogc3RyaW5nLCBvcHRpb25zOiBhbnkpIHtcbiAgICAgICAgICByZXEuY29va2llcy5zZXQoe1xuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIHZhbHVlOiAnJyxcbiAgICAgICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmVzID0gTmV4dFJlc3BvbnNlLm5leHQoe1xuICAgICAgICAgICAgcmVxdWVzdDoge1xuICAgICAgICAgICAgICBoZWFkZXJzOiByZXEuaGVhZGVycyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmVzLmNvb2tpZXMuc2V0KHtcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICB2YWx1ZTogJycsXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9XG4gICk7XG5cbiAgLy8gR2V0IHRoZSBwYXRobmFtZSAoYWxyZWFkeSBkZWZpbmVkIGFib3ZlKVxuXG4gIC8vIFB1YmxpYyByb3V0ZXMgdGhhdCBkb24ndCByZXF1aXJlIGF1dGhlbnRpY2F0aW9uXG4gIGNvbnN0IHB1YmxpY1JvdXRlcyA9IFtcbiAgICAnLycsXG4gICAgJy9wcmljaW5nJyxcbiAgICAnL2F1dGgvc2lnbmluJyxcbiAgICAnL2F1dGgvc2lnbnVwJyxcbiAgICAnL2F1dGgvY2FsbGJhY2snLFxuICAgICcvYXV0aC92ZXJpZnktZW1haWwnLFxuICAgICcvY2hlY2tvdXQnLCAvLyBBbGxvdyBjaGVja291dCBmb3IgcGVuZGluZyBzaWdudXBzXG4gICAgJy9hcGkvc3RyaXBlL3dlYmhvb2tzJywgLy8gU3RyaXBlIHdlYmhvb2tzIHNob3VsZCBiZSBwdWJsaWNcbiAgXTtcblxuICAvLyBBUEkgcm91dGVzIHRoYXQgZG9uJ3QgcmVxdWlyZSBhdXRoZW50aWNhdGlvblxuICBjb25zdCBwdWJsaWNBcGlSb3V0ZXMgPSBbXG4gICAgJy9hcGkvc3RyaXBlL3dlYmhvb2tzJyxcbiAgICAnL2FwaS9zeXN0ZW0tc3RhdHVzJyxcbiAgICAnL2FwaS9kZWJ1ZycsIC8vIERlYnVnIGVuZHBvaW50c1xuICAgICcvYXBpL3ByaWNpbmcnLCAvLyBQcmljaW5nIGVuZHBvaW50c1xuICBdO1xuXG4gIC8vIENoZWNrIGlmIHRoZSByb3V0ZSBpcyBwdWJsaWNcbiAgY29uc3QgaXNQdWJsaWNSb3V0ZSA9IHB1YmxpY1JvdXRlcy5zb21lKHJvdXRlID0+IHBhdGhuYW1lID09PSByb3V0ZSB8fCBwYXRobmFtZS5zdGFydHNXaXRoKHJvdXRlKSk7XG4gIGNvbnN0IGlzUHVibGljQXBpUm91dGUgPSBwdWJsaWNBcGlSb3V0ZXMuc29tZShyb3V0ZSA9PiBwYXRobmFtZS5zdGFydHNXaXRoKHJvdXRlKSk7XG5cbiAgLy8gQWxsb3cgcHVibGljIHJvdXRlcyBhbmQgQVBJIHJvdXRlc1xuICBpZiAoaXNQdWJsaWNSb3V0ZSB8fCBpc1B1YmxpY0FwaVJvdXRlKSB7XG4gICAgcmV0dXJuIHJlcztcbiAgfVxuXG4gIC8vIEdldCB0aGUgdXNlciB3aXRoIGVycm9yIGhhbmRsaW5nIGZvciBuZXR3b3JrIGlzc3VlcyAobW9yZSBzZWN1cmUgdGhhbiBnZXRTZXNzaW9uKVxuICBsZXQgc2Vzc2lvbiA9IG51bGw7XG4gIHRyeSB7XG4gICAgY29uc3Qge1xuICAgICAgZGF0YTogeyB1c2VyIH0sXG4gICAgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuICAgIC8vIENyZWF0ZSBhIHNlc3Npb24tbGlrZSBvYmplY3QgZm9yIGNvbXBhdGliaWxpdHlcbiAgICBzZXNzaW9uID0gdXNlciA/IHsgdXNlciB9IDogbnVsbDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdNaWRkbGV3YXJlOiBGYWlsZWQgdG8gZ2V0IHNlc3Npb24gZnJvbSBTdXBhYmFzZTonLCBlcnJvcik7XG4gICAgLy8gSWYgd2UgY2FuJ3QgY29ubmVjdCB0byBTdXBhYmFzZSwgYWxsb3cgdGhlIHJlcXVlc3QgdG8gcHJvY2VlZFxuICAgIC8vIFRoaXMgcHJldmVudHMgdGhlIGVudGlyZSBhcHAgZnJvbSBiZWluZyBibG9ja2VkIGJ5IG5ldHdvcmsgaXNzdWVzXG4gICAgcmV0dXJuIHJlcztcbiAgfVxuXG4gIC8vIElmIG5vIHNlc3Npb24gYW5kIHRyeWluZyB0byBhY2Nlc3MgcHJvdGVjdGVkIHJvdXRlLCByZWRpcmVjdCB0byBzaWduaW5cbiAgaWYgKCFzZXNzaW9uKSB7XG4gICAgY29uc3QgcmVkaXJlY3RVcmwgPSBuZXcgVVJMKCcvYXV0aC9zaWduaW4nLCByZXEudXJsKTtcbiAgICByZWRpcmVjdFVybC5zZWFyY2hQYXJhbXMuc2V0KCdyZWRpcmVjdFRvJywgcGF0aG5hbWUpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QocmVkaXJlY3RVcmwpO1xuICB9XG5cbiAgLy8gRm9yIGF1dGhlbnRpY2F0ZWQgdXNlcnMsIGNoZWNrIGlmIHRoZXkncmUgYWNjZXNzaW5nIGRhc2hib2FyZCByb3V0ZXNcbiAgaWYgKHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9kYXNoYm9hcmQnKSB8fCBwYXRobmFtZS5zdGFydHNXaXRoKCcvcGxheWdyb3VuZCcpIHx8IHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9sb2dzJykgfHwgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL215LW1vZGVscycpIHx8IHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9hcGkta2V5cycpIHx8IHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9jb25maWd1cmF0aW9ucycpKSB7XG4gICAgLy8gQ2hlY2sgc3Vic2NyaXB0aW9uIHN0YXR1cyBmb3IgcHJvdGVjdGVkIGFwcCByb3V0ZXNcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhOiBwcm9maWxlLCBlcnJvcjogcHJvZmlsZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgIC5zZWxlY3QoJ3N1YnNjcmlwdGlvbl9zdGF0dXMsIHN1YnNjcmlwdGlvbl90aWVyJylcbiAgICAgICAgLmVxKCdpZCcsIHNlc3Npb24udXNlci5pZClcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAocHJvZmlsZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ01pZGRsZXdhcmU6IEVycm9yIGZldGNoaW5nIHVzZXIgcHJvZmlsZTonLCBwcm9maWxlRXJyb3IpO1xuICAgICAgICAvLyBJZiB3ZSBjYW4ndCBmZXRjaCBwcm9maWxlIGR1ZSB0byBuZXR3b3JrIGlzc3VlcywgYWxsb3cgYWNjZXNzXG4gICAgICAgIHJldHVybiByZXM7XG4gICAgICB9XG5cbiAgICAgIC8vIElmIG5vIHByb2ZpbGUgb3IgaW5hY3RpdmUgc3Vic2NyaXB0aW9uLCByZWRpcmVjdCB0byBwcmljaW5nXG4gICAgICBpZiAoIXByb2ZpbGUgfHwgcHJvZmlsZS5zdWJzY3JpcHRpb25fc3RhdHVzICE9PSAnYWN0aXZlJykge1xuICAgICAgICBjb25zdCByZWRpcmVjdFVybCA9IG5ldyBVUkwoJy9wcmljaW5nJywgcmVxLnVybCk7XG4gICAgICAgIHJlZGlyZWN0VXJsLnNlYXJjaFBhcmFtcy5zZXQoJ2NoZWNrb3V0JywgJ3RydWUnKTtcbiAgICAgICAgcmVkaXJlY3RVcmwuc2VhcmNoUGFyYW1zLnNldCgnbWVzc2FnZScsICdzdWJzY3JpcHRpb25fcmVxdWlyZWQnKTtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdChyZWRpcmVjdFVybCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIHN1YnNjcmlwdGlvbiBzdGF0dXMgaW4gbWlkZGxld2FyZTonLCBlcnJvcik7XG4gICAgICAvLyBPbiBlcnJvciwgcmVkaXJlY3QgdG8gcHJpY2luZyB0byBiZSBzYWZlXG4gICAgICBjb25zdCByZWRpcmVjdFVybCA9IG5ldyBVUkwoJy9wcmljaW5nJywgcmVxLnVybCk7XG4gICAgICByZWRpcmVjdFVybC5zZWFyY2hQYXJhbXMuc2V0KCdjaGVja291dCcsICd0cnVlJyk7XG4gICAgICByZWRpcmVjdFVybC5zZWFyY2hQYXJhbXMuc2V0KCdtZXNzYWdlJywgJ3N1YnNjcmlwdGlvbl9jaGVja19mYWlsZWQnKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QocmVkaXJlY3RVcmwpO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiByZXM7XG59XG5cbmV4cG9ydCBjb25zdCBjb25maWcgPSB7XG4gIG1hdGNoZXI6IFtcbiAgICAvKlxuICAgICAqIE1hdGNoIGFsbCByZXF1ZXN0IHBhdGhzIGV4Y2VwdCBmb3IgdGhlIG9uZXMgc3RhcnRpbmcgd2l0aDpcbiAgICAgKiAtIF9uZXh0L3N0YXRpYyAoc3RhdGljIGZpbGVzKVxuICAgICAqIC0gX25leHQvaW1hZ2UgKGltYWdlIG9wdGltaXphdGlvbiBmaWxlcylcbiAgICAgKiAtIGZhdmljb24uaWNvIChmYXZpY29uIGZpbGUpXG4gICAgICogLSBwdWJsaWMgZm9sZGVyXG4gICAgICovXG4gICAgJy8oKD8hX25leHQvc3RhdGljfF9uZXh0L2ltYWdlfGZhdmljb24uaWNvfHB1YmxpYy8pLiopJyxcbiAgXSxcbn07XG4iXSwibmFtZXMiOlsiY3JlYXRlU2VydmVyQ2xpZW50IiwiTmV4dFJlc3BvbnNlIiwibWlkZGxld2FyZSIsInJlcSIsInJlcyIsIm5leHQiLCJyZXF1ZXN0IiwiaGVhZGVycyIsInBhdGhuYW1lIiwibmV4dFVybCIsInN0YXJ0c1dpdGgiLCJpbmNsdWRlcyIsInByb2Nlc3MiLCJlbnYiLCJCWVBBU1NfQVVUSF9NSURETEVXQVJFIiwiY29uc29sZSIsImxvZyIsInN1cGFiYXNlIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJjb29raWVzIiwiZ2V0IiwibmFtZSIsInZhbHVlIiwic2V0Iiwib3B0aW9ucyIsInJlbW92ZSIsInB1YmxpY1JvdXRlcyIsInB1YmxpY0FwaVJvdXRlcyIsImlzUHVibGljUm91dGUiLCJzb21lIiwicm91dGUiLCJpc1B1YmxpY0FwaVJvdXRlIiwic2Vzc2lvbiIsImRhdGEiLCJ1c2VyIiwiYXV0aCIsImdldFVzZXIiLCJlcnJvciIsInJlZGlyZWN0VXJsIiwiVVJMIiwidXJsIiwic2VhcmNoUGFyYW1zIiwicmVkaXJlY3QiLCJwcm9maWxlIiwicHJvZmlsZUVycm9yIiwiZnJvbSIsInNlbGVjdCIsImVxIiwiaWQiLCJzaW5nbGUiLCJzdWJzY3JpcHRpb25fc3RhdHVzIiwiY29uZmlnIiwibWF0Y2hlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});